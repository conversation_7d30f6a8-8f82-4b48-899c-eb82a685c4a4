"use server";

import { client, clientWithToken } from "@/sanity/lib/client";
import { sendConfirmationEmail } from "../email/newsletter";
import {
  newsletterSchema,
  type NewsletterFormData,
} from "../schemas/newsletter";
import crypto from "crypto";
import { z } from "zod";

export type SubscribeResult = {
  success: boolean;
  error?: string;
};

export async function subscribeToNewsletter(
  data: NewsletterFormData,
): Promise<SubscribeResult> {
  try {
    const validatedData = newsletterSchema.parse(data);

    const existingSubscriber = await client.fetch(
      `*[_type == "subscriber" && email == $email][0]`,
      { email: validatedData.email },
    );

    if (existingSubscriber) {
      if (existingSubscriber.status === "active") {
        return {
          success: false,
          error: "Este email já está inscrito na newsletter",
        };
      } else if (existingSubscriber.status === "unsubscribed") {
        const unsubscribeToken = crypto.randomUUID();

        // Store original data for rollback
        const originalData = {
          status: existingSubscriber.status,
          subscribedAt: existingSubscriber.subscribedAt,
          unsubscribedAt: existingSubscriber.unsubscribedAt,
          unsubscribeToken: existingSubscriber.unsubscribeToken,
        };

        // Update subscriber status
        await clientWithToken
          .patch(existingSubscriber._id)
          .set({
            status: "active",
            subscribedAt: new Date().toISOString(),
            unsubscribeToken,
          })
          .unset(["unsubscribedAt"])
          .commit();

        try {
          // Attempt to send confirmation email
          await sendConfirmationEmail(validatedData.email, unsubscribeToken);
          return { success: true };
        } catch (emailError) {
          console.error(
            "Email sending failed, rolling back subscription:",
            emailError,
          );

          // Rollback: restore original subscriber data
          const rollbackPatch = clientWithToken
            .patch(existingSubscriber._id)
            .set({
              status: originalData.status,
              unsubscribeToken: originalData.unsubscribeToken,
            });

          if (originalData.subscribedAt) {
            rollbackPatch.set({ subscribedAt: originalData.subscribedAt });
          } else {
            rollbackPatch.unset(["subscribedAt"]);
          }

          if (originalData.unsubscribedAt) {
            rollbackPatch.set({ unsubscribedAt: originalData.unsubscribedAt });
          }

          await rollbackPatch.commit();

          return {
            success: false,
            error:
              "Falha ao enviar email de confirmação. Tente novamente mais tarde.",
          };
        }
      }
    }

    // New subscriber flow
    const unsubscribeToken = crypto.randomUUID();
    let createdSubscriberId: string | null = null;

    try {
      // Create new subscriber
      const result = await clientWithToken.create({
        _type: "subscriber",
        email: validatedData.email,
        status: "active",
        subscribedAt: new Date().toISOString(),
        unsubscribeToken,
      });

      createdSubscriberId = result._id;

      // Attempt to send confirmation email
      await sendConfirmationEmail(validatedData.email, unsubscribeToken);
      return { success: true };
    } catch (emailError) {
      console.error(
        "Email sending failed, rolling back subscription:",
        emailError,
      );

      // Rollback: delete the created subscriber if email failed
      if (createdSubscriberId) {
        try {
          await clientWithToken.delete(createdSubscriberId);
        } catch (deleteError) {
          console.error("Failed to rollback subscriber creation:", deleteError);
        }
      }

      return {
        success: false,
        error:
          "Falha ao enviar email de confirmação. Tente novamente mais tarde.",
      };
    }
  } catch (error) {
    console.error("Newsletter subscription error:", error);

    if (error instanceof z.ZodError) {
      return {
        success: false,
        error: "Dados inválidos fornecidos",
      };
    }

    return {
      success: false,
      error: "Erro interno do servidor. Tente novamente mais tarde.",
    };
  }
}

export async function unsubscribeFromNewsletter(
  token: string,
): Promise<SubscribeResult> {
  try {
    if (!token) {
      return {
        success: false,
        error: "Token de cancelamento inválido",
      };
    }

    const subscriber = await client.fetch(
      `*[_type == "subscriber" && unsubscribeToken == $token][0]`,
      { token } as any,
    );

    if (!subscriber) {
      return {
        success: false,
        error: "Token de cancelamento inválido ou expirado",
      };
    }

    if (subscriber.status === "unsubscribed") {
      return {
        success: false,
        error: "Esta assinatura já foi cancelada",
      };
    }

    await clientWithToken
      .patch(subscriber._id)
      .set({
        status: "unsubscribed",
        unsubscribedAt: new Date().toISOString(),
      })
      .commit();

    return { success: true };
  } catch (error) {
    console.error("Newsletter unsubscribe error:", error);
    return {
      success: false,
      error: "Erro interno do servidor. Tente novamente mais tarde.",
    };
  }
}
