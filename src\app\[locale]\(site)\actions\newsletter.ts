"use server";

import { client, clientWithToken } from "@/sanity/lib/client";
import { sendConfirmationEmail } from "../email/newsletter";
import {
  newsletterSchema,
  type NewsletterFormData,
} from "../schemas/newsletter";
import crypto from "crypto";
import { z } from "zod";

export type SubscribeResult = {
  success: boolean;
  error?: string;
};

export async function subscribeToNewsletter(
  data: NewsletterFormData,
): Promise<SubscribeResult> {
  try {
    const validatedData = newsletterSchema.parse(data);

    const existingSubscriber = await client.fetch(
      `*[_type == "subscriber" && email == $email][0]`,
      { email: validatedData.email },
    );

    if (existingSubscriber) {
      if (existingSubscriber.status === "active") {
        return {
          success: false,
          error: "Este email já está inscrito na newsletter",
        };
      } else if (existingSubscriber.status === "unsubscribed") {
        const unsubscribeToken = crypto.randomUUID();

        await clientWithToken
          .patch(existingSubscriber._id)
          .set({
            status: "active",
            subscribedAt: new Date().toISOString(),
            unsubscribeToken,
          })
          .unset(["unsubscribedAt"])
          .commit();

        await sendConfirmationEmail(validatedData.email, unsubscribeToken);

        return { success: true };
      }
    }

    const unsubscribeToken = crypto.randomUUID();

    await clientWithToken.create({
      _type: "subscriber",
      email: validatedData.email,
      status: "active",
      subscribedAt: new Date().toISOString(),
      unsubscribeToken,
    });

    await sendConfirmationEmail(validatedData.email, unsubscribeToken);

    return { success: true };
  } catch (error) {
    console.error("Newsletter subscription error:", error);

    if (error instanceof z.ZodError) {
      return {
        success: false,
        error: "Dados inválidos fornecidos",
      };
    }

    return {
      success: false,
      error: "Erro interno do servidor. Tente novamente mais tarde.",
    };
  }
}

export async function unsubscribeFromNewsletter(
  token: string,
): Promise<SubscribeResult> {
  try {
    if (!token) {
      return {
        success: false,
        error: "Token de cancelamento inválido",
      };
    }

    const subscriber = await client.fetch(
      `*[_type == "subscriber" && unsubscribeToken == $token][0]`,
      { token } as any,
    );

    if (!subscriber) {
      return {
        success: false,
        error: "Token de cancelamento inválido ou expirado",
      };
    }

    if (subscriber.status === "unsubscribed") {
      return {
        success: false,
        error: "Esta assinatura já foi cancelada",
      };
    }

    await clientWithToken
      .patch(subscriber._id)
      .set({
        status: "unsubscribed",
        unsubscribedAt: new Date().toISOString(),
      })
      .commit();

    return { success: true };
  } catch (error) {
    console.error("Newsletter unsubscribe error:", error);
    return {
      success: false,
      error: "Erro interno do servidor. Tente novamente mais tarde.",
    };
  }
}
