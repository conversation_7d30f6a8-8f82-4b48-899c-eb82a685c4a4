import { subscriberStatus } from "@/app/[locale]/(site)/schemas/newsletter";
import { defineType, defineField } from "sanity";

export const subscriberType = defineType({
  name: "subscriber",
  title: "<PERSON>sin<PERSON>",
  type: "document",
  fields: [
    defineField({
      name: "email",
      title: "E-mail",
      type: "email",
      description: "Endereço de e-mail do assinante",
      validation: (Rule) =>
        Rule.required().error("Por favor, insira um endereço de e-mail válido"),
      readOnly: false, // Permite edição apenas na criação
    }),
    defineField({
      name: "status",
      title: "Estado",
      type: "string",
      description: "Estado da assinatura",
      options: {
        list: Object.entries(subscriberStatus).map(([value, title]) => ({
          value,
          title,
        })),
        layout: "radio",
      },
      initialValue: "active",
      readOnly: true,
      validation: (Rule) => Rule.required().error("O status é obrigatório"),
    }),
    defineField({
      name: "subscribedAt",
      title: "Data de Assinatura",
      type: "datetime",
      description: "Data e hora em que o assinante se inscreveu",
      initialValue: () => new Date().toISOString(),
      readOnly: true,
      options: {
        dateFormat: "DD/MM/YYYY",
        timeFormat: "HH:mm",
      },
    }),
    defineField({
      name: "unsubscribedAt",
      title: "Data de Cancelamento",
      type: "datetime",
      description: "Data e hora em que o assinante cancelou a assinatura",
      readOnly: true, // Campo somente leitura
      hidden: ({ document }) => document?.status !== "unsubscribed",
      options: {
        dateFormat: "DD/MM/YYYY",
        timeFormat: "HH:mm",
      },
    }),
    defineField({
      name: "unsubscribeToken",
      title: "Token de Cancelamento",
      type: "string",
      description: "Token único para cancelamento da assinatura",
      readOnly: true, // Campo somente leitura
      validation: (Rule) => Rule.required().error("O token é obrigatório"),
    }),
  ],
  preview: {
    select: {
      title: "email",
      status: "status",
      subscribedAt: "subscribedAt",
    },
    prepare(selection) {
      const { title, status, subscribedAt } = selection;
      const statusLabel = status === "active" ? "Ativo" : "Cancelado";
      const date = subscribedAt
        ? new Date(subscribedAt).toLocaleDateString("pt-BR")
        : "";
      return {
        title: title || "E-mail não informado",
        subtitle: `${statusLabel}${date ? ` • ${date}` : ""}`,
      };
    },
  },
  orderings: [
    {
      title: "Mais recente primeiro",
      name: "subscribedDesc",
      by: [{ field: "subscribedAt", direction: "desc" }],
    },
    {
      title: "Por status",
      name: "statusAsc",
      by: [{ field: "status", direction: "asc" }],
    },
    {
      title: "Por e-mail (A-Z)",
      name: "emailAsc",
      by: [{ field: "email", direction: "asc" }],
    },
  ],
});
