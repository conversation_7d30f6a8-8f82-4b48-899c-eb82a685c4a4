"use client";

import { useTransition } from "react";
import { useForm } from "react-hook-form";
import { toast } from "sonner";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Link } from "@/i18n/navigation";
import { socialsItems } from "./SocialButtons";
import { useTranslations } from "next-intl";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { subscribeToNewsletter } from "../actions/newsletter";
import {
  newsletterSchema,
  type NewsletterFormData,
} from "../schemas/newsletter";
import { standardSchemaResolver } from "@hookform/resolvers/standard-schema";

const NewsletterForm = () => {
  const t = useTranslations("Footer");
  const [isPending, startTransition] = useTransition();

  const form = useForm<NewsletterFormData>({
    resolver: standardSchemaResolver(newsletterSchema),
    defaultValues: {
      email: "",
      // name: "",
    },
  });

  const onSubmit = async (data: NewsletterFormData) => {
    startTransition(async () => {
      try {
        const result = await subscribeToNewsletter(data);

        if (result.success) {
          toast.success(t("newsletter.success"));
          form.reset();
        } else {
          toast.error(result.error || t("newsletter.error"));
        }
      } catch (error) {
        console.error("Newsletter subscription error:", error);
        toast.error(t("newsletter.error"));
      }
    });
  };

  return (
    <div>
      <h5 className="mb-2">{t("newsletter.title")}</h5>
      <Form {...form}>
        <form
          onSubmit={form.handleSubmit(onSubmit)}
          className="flex flex-col items-start gap-4"
        >
          <FormField
            control={form.control}
            name="email"
            render={({ field }) => (
              <FormItem className="w-full max-w-sm">
                <FormLabel className="sr-only">
                  {t("newsletter.emailLabel")}
                </FormLabel>
                <FormControl>
                  <Input
                    type="email"
                    placeholder={t("newsletter.emailPlaceholder")}
                    className="bg-primary-foreground focus:border-background rounded-none border-transparent [box-shadow:inset_0_0_10px_5px_rgba(0,0,0,0.5)]"
                    disabled={isPending}
                    {...field}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
          <span className="flex w-full flex-wrap items-center justify-between gap-4">
            <Button
              type="submit"
              variant="outline"
              className="border-background text-background"
              disabled={isPending}
            >
              {isPending ? t("newsletter.submitting") : t("newsletter.submit")}
            </Button>
            <span className="hidden items-center gap-4 sm:flex">
              {socialsItems.map(({ href, Icon }, index) => {
                return (
                  <Link key={index} href={href}>
                    <Icon
                      className="size-7"
                      strokeWidth={1.25}
                      innerPathClassName="fill-primary-foreground"
                    />
                  </Link>
                );
              })}
            </span>
          </span>
        </form>
      </Form>
    </div>
  );
};

export default NewsletterForm;
